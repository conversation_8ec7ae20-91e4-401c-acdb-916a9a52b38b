import tinycolor from 'tinycolor2';
import {colors} from './colors';

const primary = colors.grey200; // Main primary color
const secondary = colors.secondary;
const error = '#ef4444'; // Red for errors
const warning = '#f59e0b'; // Orange for warnings
const success = '#10b981'; // Green for success
const info = '#3b82f6'; // Blue for info

// Dark theme specific colors
const darkPrimary = colors.dark;
const darkSecondary = '#94a3b8'; // Light gray for dark theme secondary

const lightenRate = 7.5;
const darkenRate = 15;

export const paletteConfig = {
  light: {
    background: {
      default: colors.white,
      paper: colors.white,
    },
    primary: {
      main: primary,
      light: tinycolor(primary).lighten(lightenRate).toHexString(),
      dark: tinycolor(primary).darken(darkenRate).toHexString(),
    },
    secondary: {
      main: secondary,
      light: tinycolor(secondary).lighten(lightenRate).toHexString(),
      dark: tinycolor(secondary).darken(darkenRate).toHexString(),
    },
    error: {
      main: error,
      light: tinycolor(error).lighten(lightenRate).toHexString(),
      dark: tinycolor(error).darken(darkenRate).toHexString(),
    },
    warning: {
      main: warning,
      light: tinycolor(warning).lighten(lightenRate).toHexString(),
      dark: tinycolor(warning).darken(darkenRate).toHexString(),
    },
    success: {
      main: success,
      light: tinycolor(success).lighten(lightenRate).toHexString(),
      dark: tinycolor(success).darken(darkenRate).toHexString(),
    },
    info: {
      main: info,
      light: tinycolor(info).lighten(lightenRate).toHexString(),
      dark: tinycolor(info).darken(darkenRate).toHexString(),
    },
    text: {
      primary: colors.black,
      secondary: colors.mediumGrey,
    },
    divider: '#e0e0e0',
  },
  dark: {
    background: {
      default: '#0f172a',
      paper: '#1e293b',
    },
    primary: {
      main: darkPrimary,
      light: tinycolor(darkPrimary).lighten(lightenRate).toHexString(),
      dark: tinycolor(darkPrimary).darken(darkenRate).toHexString(),
    },
    secondary: {
      main: darkSecondary,
      light: tinycolor(darkSecondary).lighten(lightenRate).toHexString(),
      dark: tinycolor(darkSecondary).darken(darkenRate).toHexString(),
    },
    error: {
      main: tinycolor(error).lighten(5).toHexString(), // Slightly lighter for dark theme
      light: tinycolor(error).lighten(lightenRate).toHexString(),
      dark: tinycolor(error).darken(darkenRate).toHexString(),
    },
    warning: {
      main: tinycolor(warning).lighten(5).toHexString(), // Slightly lighter for dark theme
      light: tinycolor(warning).lighten(lightenRate).toHexString(),
      dark: tinycolor(warning).darken(darkenRate).toHexString(),
    },
    success: {
      main: tinycolor(success).lighten(5).toHexString(), // Slightly lighter for dark theme
      light: tinycolor(success).lighten(lightenRate).toHexString(),
      dark: tinycolor(success).darken(darkenRate).toHexString(),
    },
    info: {
      main: darkPrimary, // Use same as primary for consistency
      light: tinycolor(darkPrimary).lighten(lightenRate).toHexString(),
      dark: tinycolor(darkPrimary).darken(darkenRate).toHexString(),
    },
    text: {
      primary: '#f8fafc',
      secondary: '#cbd5e1',
    },
    divider: '#374151',
    border: {
      main: '#ffffff1f',
    },
  },
};

export const commonConfig = {
  typography: {
    fontFamily: ['Lato', 'Inter', 'Roboto', 'Arial', 'sans-serif'].join(','),
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h6: {
      fontSize: '1.125rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
    },
    button: {
      textTransform: 'none' as const,
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: '0.5rem', // 8px converted to rem (8/16 = 0.5)
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        '*::-webkit-scrollbar': {
          height: '0.5rem', // 8px converted to rem
          width: '0.5rem',
        },
        '*::-webkit-scrollbar-track': {
          background: '#fafafa',
        },
        '*::-webkit-scrollbar-thumb': {
          backgroundColor: '#d1d1d1',
          borderRadius: '6.25rem', // 100px converted to rem
          opacity: '0.5',
        },
        '*::-webkit-scrollbar-thumb:horizontal': {
          width: '0.5rem', // 8px converted to rem
          height: '18.75rem', // 300px converted to rem
          backgroundColor: '#d1d1d1',
          backgroundClip: 'padding-box',
          borderRight: '1rem white solid', // 16px converted to rem
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: '0.5rem', // 8px converted to rem
          padding: '0.75rem 1.5rem', // 12px 24px converted to rem
          fontSize: '0.9rem',
          fontWeight: 500,
          textTransform: 'none' as const,
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 0.25rem 0.5rem #00000020', // rgba converted to hex with opacity
          },
        },
        contained: {
          '&:hover': {
            boxShadow: '0 0.25rem 0.75rem #00000026', // rgba converted to hex with opacity
          },
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          borderRadius: '0.5rem', // 8px converted to rem
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: '#d1d5db',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderWidth: '0.125rem', // 2px converted to rem
          },
        },
      },
    },
    MuiFormLabel: {
      styleOverrides: {
        root: {
          fontSize: '0.875rem',
          fontWeight: 500,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: '0.75rem', // 12px converted to rem
          boxShadow: '0 0.0625rem 0.1875rem #0000001a', // rgba converted to hex with opacity
        },
      },
    },
  },
};
