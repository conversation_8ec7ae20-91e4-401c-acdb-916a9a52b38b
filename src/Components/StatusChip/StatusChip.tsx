import Chip from '@mui/material/Chip';
import React from 'react';

interface IStatusChipProps {
  label: string;
  backgroundColor: string;
  color: string;
}

const StatusChip: React.FC<IStatusChipProps> = ({ label, backgroundColor, color }) => {
  return (
    <Chip
      data-testid="status-chip"
      label={label}
      sx={{
        backgroundColor: `${backgroundColor}`,
        color: color,
        paddingTop: 0.5,
        paddingBottom: 0.5,
        fontSize: '0.75rem',
        height: '1.5rem',
        borderRadius: '0.4rem',
        fontWeight: 700,
      }}
    />
  );
};

export default StatusChip;
