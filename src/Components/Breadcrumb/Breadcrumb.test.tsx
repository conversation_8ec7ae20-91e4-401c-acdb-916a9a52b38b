import '@testing-library/jest-dom';
import {render, screen} from '@testing-library/react';
import {MemoryRouter} from 'react-router-dom';
import {vi} from 'vitest';
import Breadcrumb, {BreadcrumbItem} from './Breadcrumb';

// Mock the colors module to ensure consistent values
vi.mock('Providers/theme/colors', () => ({
  colors: {
    dark500: '#6B6B6B',
    dark300: '#9D9D9D',
    black: '#000000',
  },
}));

describe('Breadcrumb Component', () => {
  const breadcrumbItems: BreadcrumbItem[] = [{label: 'Tenants', url: '/tenants'}, {label: 'Add Tenant'}];

  it('renders all breadcrumb items', () => {
    render(
      <MemoryRouter>
        <Breadcrumb items={breadcrumbItems} />
      </MemoryRouter>,
    );

    expect(screen.getByText('Tenants')).toBeInTheDocument();
    expect(screen.getByText('Add Tenant')).toBeInTheDocument();
  });

  it('renders links for items with url', () => {
    render(
      <MemoryRouter>
        <Breadcrumb items={breadcrumbItems} />
      </MemoryRouter>,
    );

    const tenantLink = screen.getByText('Tenants');
    expect(tenantLink.tagName).toBe('A'); // Should render <Link> -> <a>
    expect(tenantLink).toHaveAttribute('href', '/tenants');
  });

  it('renders last item as bold with underline', () => {
    render(
      <MemoryRouter>
        <Breadcrumb items={breadcrumbItems} />
      </MemoryRouter>,
    );

    const lastItem = screen.getByText('Add Tenant');

    // Check that the last item has the correct styles
    expect(lastItem).toHaveStyle('font-weight: bold');
    expect(lastItem).toHaveStyle('color: rgb(0, 0, 0)');
    expect(lastItem).toHaveStyle('padding-bottom: 0.125rem');
    expect(lastItem).toHaveStyle('border-bottom: 0.0625rem solid rgb(0, 0, 0)');
  });

  it('shows separator between items', () => {
    render(
      <MemoryRouter>
        <Breadcrumb items={breadcrumbItems} />
      </MemoryRouter>,
    );

    expect(screen.getByText('|')).toBeInTheDocument();
  });

  it('uses custom separator when provided', () => {
    render(
      <MemoryRouter>
        <Breadcrumb items={breadcrumbItems} separator=">" />
      </MemoryRouter>,
    );

    expect(screen.getByText('>')).toBeInTheDocument();
  });

  it('renders header when showHeader is true', () => {
    render(
      <MemoryRouter>
        <Breadcrumb items={breadcrumbItems} showHeader />
      </MemoryRouter>,
    );

    const header = screen.getByRole('heading', {level: 2});
    expect(header).toHaveTextContent('Add Tenant');
  });

  it('renders empty breadcrumb when items are not provided', () => {
    render(
      <MemoryRouter>
        <Breadcrumb />
      </MemoryRouter>,
    );

    expect(screen.queryByRole('link')).not.toBeInTheDocument();
  });
});
