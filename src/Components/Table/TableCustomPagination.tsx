import {
  Box,
  FormControl,
  MenuItem,
  Pagination,
  Select,
  Typography
} from '@mui/material';
import { colors } from 'Providers/theme/colors';
import { memo, useCallback, useEffect } from 'react';

export interface TablePaginationProps {
  currentPage: number;
  currentRowsPerPage: number;
  totalCount: number;
  rowsPerPageOptions?: number[];
  onPageChange?: (page: number) => void;
  onRowsPerPageChange?: (rowsPerPage: number) => void;
  setLimit?: (newLimit: number) => void;
  setOffset?: (newOffset: number) => void;
  showEntriesText?: boolean;
  disabled?: boolean;
}

const DEFAULT_ROWS_PER_PAGE_OPTIONS = [10, 20, 50];

// Styles extracted as constants for better performance
const CONTAINER_STYLES = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  p: 2,
  backgroundColor: 'white',
} as const;

const ROWS_PER_PAGE_CONTAINER_STYLES = {
  display: 'flex',
  alignItems: 'center',
  gap: 1,
} as const;

const TEXT_STYLES = {
  fontSize: '0.8125rem',
  color: colors.dark700,
  fontWeight: 600,
} as const;

const SELECT_STYLES = {
  fontSize: '0.875rem',
  minWidth: 60,
  '& .MuiOutlinedInput-notchedOutline': {
    border: '0.0625rem solid' + colors.dark100
  }
} as const;

const PAGINATION_STYLES = {
  '& .MuiPaginationItem-root': {
    fontSize: '0.875rem',
    minWidth: '2rem',
    height: '2rem',
    margin: '0 0.125rem',
    color: colors.dark800,
    borderRadius: '0.5rem',
    border: '0.0625rem solid' + colors.dark100,
    backgroundColor: colors.white,
    '&:hover': {
      backgroundColor: colors.dark100,
    },
    '&.Mui-selected': {
      backgroundColor: colors.secondary,
      color: colors.white,
      border: '0.0625rem solid' + colors.dark100,
      '&:hover': {
        backgroundColor: colors.dark100,
      },
    },
  },
  '& .MuiPaginationItem-ellipsis': {
    border: 'none',
    backgroundColor: 'transparent',
  },
  '& .MuiPaginationItem-previousNext': {
    backgroundColor: colors.white,
    border: '0.0625rem solid' + colors.dark100,
    '&:hover': {
      backgroundColor: colors.dark100,
    },
  },
} as const;

const TableCustomPagination: React.FC<TablePaginationProps> = ({
  currentPage,
  currentRowsPerPage,
  totalCount,
  rowsPerPageOptions = DEFAULT_ROWS_PER_PAGE_OPTIONS,
  onPageChange,
  onRowsPerPageChange,
  setLimit,
  setOffset,
  showEntriesText = true,
  disabled = false,
}) => {
  const totalPages = Math.ceil(totalCount / currentRowsPerPage);

  const handleRowsPerPageChange = useCallback((newLimit: number) => {
    if (setLimit) {
      setLimit(newLimit);
    }
    if (setOffset) {
      setOffset(0); // Reset to first page
    }
    if (onRowsPerPageChange) {
      onRowsPerPageChange(newLimit);
    }
  }, [setLimit, setOffset, onRowsPerPageChange]);

  // Ensure currentRowsPerPage is in the available options, fallback to first option if not
  const validRowsPerPage = rowsPerPageOptions.includes(currentRowsPerPage)
    ? currentRowsPerPage
    : rowsPerPageOptions[0];

  // Auto-correct invalid rowsPerPage values
  useEffect(() => {
    if (!rowsPerPageOptions.includes(currentRowsPerPage) && rowsPerPageOptions.length > 0) {
      handleRowsPerPageChange(rowsPerPageOptions[0]);
    }
  }, [currentRowsPerPage, rowsPerPageOptions, handleRowsPerPageChange]);

  const handlePageChange = useCallback((_: unknown, page: number) => {
    const newOffset = (page - 1) * currentRowsPerPage;
    if (setOffset) {
      setOffset(newOffset);
    }
    if (onPageChange) {
      onPageChange(page);
    }
  }, [currentRowsPerPage, setOffset, onPageChange]);

  return (
    <Box sx={CONTAINER_STYLES}>
      {/* Rows per page selector */}
      <Box sx={ROWS_PER_PAGE_CONTAINER_STYLES}>
        {showEntriesText && (
          <Typography sx={TEXT_STYLES}>
            Show
          </Typography>
        )}
        <FormControl size="small" disabled={disabled}>
          <Select
            value={validRowsPerPage}
            onChange={(e) => handleRowsPerPageChange(Number(e.target.value))}
            sx={SELECT_STYLES}
            disabled={disabled}
          >
            {rowsPerPageOptions.map((option) => (
              <MenuItem key={option} value={option}>
                {option}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        {showEntriesText && (
          <Typography sx={TEXT_STYLES}>
            entries
          </Typography>
        )}
      </Box>

      {/* Pagination controls */}
      <Pagination
        count={totalPages}
        page={currentPage}
        onChange={handlePageChange}
        shape="rounded"
        siblingCount={2}
        boundaryCount={1}
        disabled={disabled}
        sx={PAGINATION_STYLES}
      />
    </Box>
  );
};

export default memo(TableCustomPagination);
