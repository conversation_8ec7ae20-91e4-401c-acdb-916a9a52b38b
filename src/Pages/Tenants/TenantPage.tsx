import { Box, IconButton, InputAdornment, List, ListItemButton, ListItemText, Paper, Popper, Stack, TextField, Tooltip, Typography } from '@mui/material';
import EyeIcon from 'Assets/EyeIcon';
import { Table } from 'Components/Table';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Add, Search } from '@mui/icons-material';
import { CellContext } from '@tanstack/react-table';
import DotIcon from 'Assets/DotIcon';
import EditIcon from 'Assets/EditIcon';
import FilterIcon from 'Assets/FilterIcon';
import Button from 'Components/Button';
import { colors } from 'Providers/theme/colors';
import { useGetTenantsCountQuery, useGetTenantsQuery } from 'redux/app/tenantManagementApiSlice';
import { actionStyles, bodyCellProps, buttonStyle, coloumnCellProps, headerBoxStyle, leftHeaderStyle, tableContainerProps, tableHeadProps, toolTipStyles } from 'styles/pages/TenantPage.styles';
import { DEFAULT_LIMIT, DEFAULT_OFFSET, getBackendColumnName, TenantStatus, tenantTableColumns } from './tenants.utils';



interface IActionButtonsProps {
  row: CellContext<unknown, unknown>;
}


export const ActionButtons: React.FC<IActionButtonsProps> = ({ row }) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [open, setOpen] = React.useState(false);

  const handleEditBtn = (): void => {
    console.log('Edit', row);
  };

  const handleDotClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
    setOpen(!open);
  };

  const handleMenuItemClick = (action: string) => {
    console.log(`${action} clicked for tenant:`, row);
    // Don't close the menu here - only close when dot icon is clicked again
  };

  // Get the tenant status from the row data
  const tenantStatus = (row.row.original as { status: TenantStatus }).status;
  const shouldShowDotIcon = tenantStatus !== TenantStatus.PENDINGONBOARDING;

  // Menu items array - conditional based on tenant status
  const menuItems = [
    { label: 'Observability', action: 'Observability' },
    // Show "Activate tenant" only if status is INACTIVE, otherwise show "Deactivate"
    tenantStatus === TenantStatus.INACTIVE
      ? { label: 'Activate tenant', action: 'Activate tenant' }
      : { label: 'Deactivate', action: 'Deactivate' }
  ];

  return (
    <Stack display="flex" flexDirection={'row'}>
      <Tooltip
        title="View details"
        placement="top"
        arrow
        slotProps={toolTipStyles}
      >
        <EyeIcon sx={actionStyles} />
      </Tooltip>
      <Tooltip
        title="Edit"
        placement="top"
        arrow
        slotProps={toolTipStyles}
      >
        <EditIcon sx={actionStyles} />
      </Tooltip>
      {shouldShowDotIcon && (
        <>
          <IconButton onClick={handleDotClick} sx={{ p: 0 }}>
            <DotIcon sx={actionStyles} />
          </IconButton>
          <Popper
            open={open}
            anchorEl={anchorEl}
            placement="bottom-end"
            disablePortal={false}
            modifiers={[
              {
                name: 'offset',
                options: {
                  offset: [0, 8],
                },
              },
            ]}
          >
            <Paper
              sx={{
                border: '1px solid #e0e0e0',
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                minWidth: 120
              }}
            >
              <List sx={{ py: 0 }}>
                {menuItems.map((item, index) => (
                  <ListItemButton
                    key={index}
                    onClick={() => handleMenuItemClick(item.action)}
                    sx={{
                      py: 0.75,
                      px: 2.5,
                      borderBottom: '1px solid #e0e0e0',
                      color: colors.dark900,
                      '&:hover': { backgroundColor: '#f5f5f5' }
                    }}
                  >
                    <ListItemText primary={item.label} />
                  </ListItemButton>
                ))}
              </List>
            </Paper>
          </Popper>
        </>
      )}
    </Stack>
  );
};

const Tenant: React.FC = () => {
  const [limit, setLimit] = useState(DEFAULT_LIMIT);
  const [searchTerm, setSearchTerm] = useState('');
  const [offset, setOffset] = useState(DEFAULT_OFFSET);
  const [sortBy, setSortBy] = useState<string | null>(null);

  const handleSortChange = (columnId: string, sort: boolean) => {
    // Map frontend column name to backend column name
    const backendColumnName = getBackendColumnName(columnId);
    const sortParam = `${backendColumnName} ${sort ? 'DESC' : 'ASC'}`;
    setSortBy(sortParam);
  };

  const handlePageChange = (page: number) => {
    const newOffset = (page - 1) * limit;
    setOffset(newOffset);
  };

  const handleRowsPerPageChange = (newLimit: number) => {
    setLimit(newLimit);
    setOffset(0); // Reset to first page when changing page size
  };

  // Build the filter object for the API call
  const filterParams = {
    limit,
    offset,
    ...(sortBy && { order: sortBy }),
    ...(searchTerm && { search: searchTerm }),
  };

  // Build count filter (without limit/offset)
  const countFilterParams = {
    ...(sortBy && { order: sortBy }),
    ...(searchTerm && { search: searchTerm }),
  };

  const { data: tenants, error, isLoading } = useGetTenantsQuery(filterParams);
  const { data: tenantsCount, error: countError, isLoading: countLoading } = useGetTenantsCountQuery(countFilterParams);
  const navigate = useNavigate();

  const handleRedirect = () => {
    navigate('/add-tenant');
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={headerBoxStyle}>
        <Typography variant='h6' sx={leftHeaderStyle}>
          Tenants
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <TextField
            size="small"
            placeholder="Search tenant name"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search sx={{ color: colors.dark100, fontSize: 25 }} />
                </InputAdornment>
              ),
            }}
            sx={{
              width: 300,
              color: colors.dark300,
              '& .MuiOutlinedInput-root': {
                backgroundColor: colors.white,
                fontSize: '1rem',
                padding: '0.125rem'
              }
            }}
          />
          <IconButton
            sx={{
              border: '0.0625rem solid' + colors.dark100,
              backgroundColor: colors.white,
              borderRadius: '0.375rem',
              width: 40,
              height: 43
            }}
          >
            <FilterIcon sx={{ color: colors.white, fontSize: 20 }} />
          </IconButton>
          <Button
            variant="contained"
            startIcon={<Add></Add>}
            sx={buttonStyle}
          >
            {/* <PlusIcon sx={{ color: 'white', backgroundColor: 'white', mr: 1 }} /> */}
            Add Tenant
          </Button>
        </Box>
      </Box>
      <Box>
        {tenants && (
          <Table
            data={tenants || []}
            columns={tenantTableColumns}
            tablePropsObject={{
              tableHeadProps: { sx: tableHeadProps },
              columnCellProps: { sx: coloumnCellProps },
              tableContainerProps: { sx: tableContainerProps },
              bodyCellProps: { sx: bodyCellProps },
            }}
            limit={limit}
            setLimit={setLimit}
            offset={offset}
            setOffset={setOffset}
            count={tenantsCount?.count || 0}
            manualPagination={true}
            enableSorting={true}
            onSortChange={handleSortChange}
            onPageChange={handlePageChange}
            onRowsPerPageChange={handleRowsPerPageChange}
          />
        )}
      </Box>
    </Box>
  );
};

export default Tenant;
