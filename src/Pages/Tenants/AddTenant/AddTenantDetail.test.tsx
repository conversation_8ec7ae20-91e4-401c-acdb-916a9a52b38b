import '@testing-library/jest-dom';
import {render, screen, waitFor} from '@testing-library/react';
import {Mock, vi} from 'vitest';
import AddTenantDetail from './AddTenantDetail';

// Mock Formik Context
vi.mock('formik', () => ({
  useFormikContext: vi.fn(),
}));

// Mock Custom Form Components
vi.mock('Components/Forms/FormInput', () => ({
  __esModule: true,
  default: ({id, placeholder, endAdornment, startAdornment, onBlur}: any) => (
    <div data-testid={`form-input-${id}`}>
      <input data-testid={`input-${id}`} placeholder={placeholder} onBlur={onBlur} />
      {endAdornment && <div data-testid={`endAdornment-${id}`}>{endAdornment}</div>}
      {startAdornment && <div data-testid={`startAdornment-${id}`}>{startAdornment}</div>}
    </div>
  ),
}));

vi.mock('Components/Forms/FormSelect/FormSelect', () => ({
  __esModule: true,
  default: ({id, placeholder}: any) => (
    <select data-testid={`form-select-${id}`}>
      <option>{placeholder}</option>
    </select>
  ),
}));

// Mock redux API call
const mockVerifyTenantKey = vi.fn();
vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useVerifyTenantKeyMutation: () => [mockVerifyTenantKey],
}));

// Mock StyleUtils (to prevent style errors)
vi.mock('Helpers/styleUtils', () => ({
  StyleUtils: {
    labelStyle: {},
    inputStyles: {},
    selectBox: {},
    labelStyleAdornment: {},
    getBaseInputStyle: {},
    inputAdornment: {},
    lalelStyles: {
      color: '#6B6B6B',
      fontSize: '0.875rem',
      fontWeight: 600,
      lineHeight: '1.125rem',
      marginBottom: '0.5rem',
      marginLeft: '0.2rem',
    },
    inputBoxStyles: {
      fontSize: '1rem',
      fontWeight: 500,
      lineHeight: '1.375rem',
      borderColor: '#DBDBDB',
      borderRadius: '0.25rem',
      padding: '0.3125rem 0.4375rem',
    },
    selectBoxStyles: {
      borderColor: '#DBDBDB',
      borderRadius: '0.25rem',
      padding: '0.1rem 0.1rem',
      height: '2.85rem',
    },
  },
}));

// Mock Constants
vi.mock('Constants/enums', () => ({
  REGISTERED_DOMAIN: '.distek.com',
}));

// Mock theme colors
vi.mock('Providers/theme/colors', () => ({
  colors: {
    green: '#008000',
    errorColor: '#A91417',
    dark400: '#838383',
    white: '#ffffff',
    lightGrey200: '#cccccc',
    errorColor100: '#A00000',
    white200: '#F0F0FB',
  },
}));

// Mock Icon
vi.mock('../../../Assets/Icons.svg', () => ({
  default: 'mocked-icon.svg',
}));

// Mock lodash debounce
vi.mock('lodash', () => ({
  debounce: (fn: any) => fn, // Return the function immediately without debouncing for tests
}));

// Import after mocks
import {useFormikContext} from 'formik';

describe('AddTenantDetail Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useFormikContext as Mock).mockReturnValue({
      values: {
        key: '',
        countryCode: {code: '+1'},
      },
      setFieldValue: vi.fn(),
      handleBlur: vi.fn(),
      isValid: true,
      errors: {},
      touched: {},
    });
  });

  it('renders all major sections', () => {
    render(<AddTenantDetail />);
    expect(screen.getByText('Tenant Information')).toBeInTheDocument();
    expect(screen.getByText('Contact Information')).toBeInTheDocument();
    expect(screen.getByTestId('form-input-company')).toBeInTheDocument();
    expect(screen.getByTestId('form-select-state')).toBeInTheDocument();
  });

  it('calls verifyTenantKey when key changes', async () => {
    (useFormikContext as Mock).mockReturnValue({
      values: {key: 'validKey', countryCode: {code: '+1'}},
      setFieldValue: vi.fn(),
      handleBlur: vi.fn(),
      isValid: true,
      errors: {},
      touched: {key: true},
    });

    mockVerifyTenantKey.mockResolvedValueOnce({
      unwrap: () => Promise.resolve({available: true}),
    });

    render(<AddTenantDetail />);
    await waitFor(() => {
      expect(mockVerifyTenantKey).toHaveBeenCalledWith({key: 'validKey'});
    });
  });

  it('shows available icon when key is available', async () => {
    (useFormikContext as Mock).mockReturnValue({
      values: {key: 'validKey', countryCode: {code: '+1'}},
      setFieldValue: vi.fn(),
      handleBlur: vi.fn(),
      isValid: true,
      errors: {},
      touched: {key: true},
    });

    mockVerifyTenantKey.mockResolvedValueOnce({
      unwrap: () => Promise.resolve({available: true}),
    });

    render(<AddTenantDetail />);
    await waitFor(() => {
      expect(screen.getByTestId('endAdornment-key')).toBeInTheDocument();
    });
  });

  it('renders startAdornment with country codes', () => {
    render(<AddTenantDetail />);
    expect(screen.getByTestId('startAdornment-mobileNumber')).toBeInTheDocument();
  });

  it('shows error icon when key is not available', async () => {
    (useFormikContext as Mock).mockReturnValue({
      values: {key: 'invalidKey', countryCode: {code: '+1'}},
      setFieldValue: vi.fn(),
      handleBlur: vi.fn(),
      isValid: true,
      errors: {},
      touched: {key: true},
    });

    mockVerifyTenantKey.mockResolvedValueOnce({
      unwrap: () => Promise.resolve({available: false, suggestions: ['suggestion1', 'suggestion2']}),
    });

    render(<AddTenantDetail />);
    await waitFor(() => {
      expect(screen.getByTestId('endAdornment-key')).toBeInTheDocument();
    });
  });

  it('does not call verifyTenantKey when in edit mode', () => {
    (useFormikContext as Mock).mockReturnValue({
      values: {key: 'validKey', countryCode: {code: '+1'}},
      setFieldValue: vi.fn(),
      handleBlur: vi.fn(),
      isValid: true,
      errors: {},
      touched: {key: true},
    });

    render(<AddTenantDetail isEdit={true} />);
    expect(mockVerifyTenantKey).not.toHaveBeenCalled();
  });

  it('renders all required form fields', () => {
    render(<AddTenantDetail />);

    // Check for all form inputs
    expect(screen.getByTestId('form-input-company')).toBeInTheDocument();
    expect(screen.getByTestId('form-input-key')).toBeInTheDocument();
    expect(screen.getByTestId('form-input-firstName')).toBeInTheDocument();
    expect(screen.getByTestId('form-input-lastName')).toBeInTheDocument();
    expect(screen.getByTestId('form-input-designation')).toBeInTheDocument();
    expect(screen.getByTestId('form-input-mobileNumber')).toBeInTheDocument();
    expect(screen.getByTestId('form-input-email')).toBeInTheDocument();
    expect(screen.getByTestId('form-input-city')).toBeInTheDocument();
    expect(screen.getByTestId('form-select-state')).toBeInTheDocument();
  });
});
