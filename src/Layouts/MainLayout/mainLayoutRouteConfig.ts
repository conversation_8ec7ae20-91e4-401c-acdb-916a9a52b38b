import AddTenant from 'Pages/Tenants/AddTenant/AddTenantPage';
import Tenant from 'Pages/Tenants/TenantPage';
// import Tenant from 'Pages/Tenants/TenantPage1';
import { lazy } from 'react';

const Home = lazy(() => import('Pages/Home'));

const mainLayoutRouteConfig = [
  {
    path: '/home',
    component: Home,
  },
  {
    path: '/tenants',
    component: Tenant,
  },
  {
    path: '/add-tenant',
    component: AddTenant,
  },
];

export default mainLayoutRouteConfig;
