import { ApiSliceIdentifier } from 'Constants/enums';
import { apiSlice } from 'redux/apiSlice';

const apiSliceIdentifier = ApiSliceIdentifier.TENANT_FACADE;
export const tenantApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getTenants: builder.query({
      query: filterQuery => ({
        url: '/tenants',
        method: 'GET',
        params: {
          filter: JSON.stringify(filterQuery)
        },
        apiSliceIdentifier,
      }),
    }),
    getTenantsCount: builder.query({
      query: (filterQuery = {}) => ({
        url: '/tenants/count',
        method: 'GET',
        params: Object.keys(filterQuery).length > 0 ? {
          filter: JSON.stringify(filterQuery)
        } : {},
        apiSliceIdentifier,
      }),
    }),
    verifyTenantKey: builder.mutation({
      query: KeyDto => ({
        url: '/tenants/verify-key',
        method: 'POST',
        body: KeyDto,
        apiSliceIdentifier,
      }),
    }),
  }),
});

export const { useVerifyTenantKeyMutation, useGetTenantsQuery, useGetTenantsCountQuery, useLazyGetTenantsCountQuery, useLazyGetTenantsQuery } = tenantApiSlice;

