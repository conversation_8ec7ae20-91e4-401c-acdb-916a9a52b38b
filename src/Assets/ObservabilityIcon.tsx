import { SvgIcon, SvgIconProps } from '@mui/material';

const ObservabilityIcon = (props: SvgIconProps) => {
    return (
        <SvgIcon {...props} viewBox="0 0 16 16">
            <path
                d="M6 4.66667H3.06667C2.6933 4.66667 2.50661 4.66667 2.36401 4.73933C2.23856 4.80324 2.13658 4.90523 2.07266 5.03067C2 5.17328 2 5.35997 2 5.73333V12.9333C2 13.3067 2 13.4934 2.07266 13.636C2.13658 13.7614 2.23856 13.8634 2.36401 13.9273C2.50661 14 2.6933 14 3.06667 14H6M6 14H10M6 14L6 3.06667C6 2.6933 6 2.50661 6.07266 2.36401C6.13658 2.23857 6.23857 2.13658 6.36401 2.07266C6.50661 2 6.6933 2 7.06667 2L8.93333 2C9.3067 2 9.49339 2 9.63599 2.07266C9.76144 2.13658 9.86342 2.23856 9.92734 2.36401C10 2.50661 10 2.6933 10 3.06667V14M10 7.33333H12.9333C13.3067 7.33333 13.4934 7.33333 13.636 7.406C13.7614 7.46991 13.8634 7.5719 13.9273 7.69734C14 7.83995 14 8.02663 14 8.4V12.9333C14 13.3067 14 13.4934 13.9273 13.636C13.8634 13.7614 13.7614 13.8634 13.636 13.9273C13.4934 14 13.3067 14 12.9333 14H10"
                stroke="#6B6B6B"
                strokeWidth="1.4"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </SvgIcon>
    );
};

export default ObservabilityIcon;